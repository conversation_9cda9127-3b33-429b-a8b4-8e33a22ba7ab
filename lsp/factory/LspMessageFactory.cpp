#include "LspMessageFactory.h"
#include "../messages/base/GenericMessages.h"
#include "../messages/base/LspError.h"
#include "../messages/lifecycle/InitializeRequest.h"
#include "../messages/lifecycle/InitializeResponse.h"
#include "../messages/lifecycle/InitializedNotification.h"
#include "../messages/lifecycle/ShutdownRequest.h"
#include "../messages/lifecycle/ShutdownResponse.h"
#include "../messages/lifecycle/ExitNotification.h"
#include "../messages/completion/CompletionRequest.h"
#include "../messages/completion/CompletionResponse.h"
#include "../messages/document/DidOpenNotification.h"
#include "../messages/document/DidChangeNotification.h"
#include "../messages/document/DidSaveNotification.h"
#include "../messages/document/DidCloseNotification.h"
#include "../messages/language/HoverRequest.h"
#include "../messages/language/HoverResponse.h"
#include "../messages/language/DefinitionRequest.h"
#include "../messages/language/DefinitionResponse.h"
#include "../messages/language/PublishDiagnosticsNotification.h"
#include <QJsonDocument>
#include <QDebug>
#include <QMetaObject>
#include <QMetaType>
#include <QMetaClassInfo>
#include <QCoreApplication>

namespace Lsp {

LspMessageFactory::LspMessageFactory() {
    // Register all LSP message classes using the automatic discovery system
    registerAllMessageClasses();
}

std::unique_ptr<LspMessage> LspMessageFactory::createMessage(const QJsonObject& json, LspMessageRegistry* registry) {
    if (!MessageUtils::isValidJsonRpc(json)) {
        qWarning() << "Invalid JSON-RPC 2.0 format";
        return nullptr;
    }

    try {
        if (MessageUtils::isError(json)) {
            return LspError::fromJson(json);
        }

        if (MessageUtils::isRequest(json)) {
            QString method = MessageUtils::getMethod(json);
            auto it = m_requestCreators.find(method);
            if (it != m_requestCreators.end()) {
                return it.value()(json);
            }
            // Create generic request
            QVariant id = MessageUtils::getId(json);
            QJsonObject params = json["params"].toObject();
            return std::make_unique<GenericRequest>(id, method, params);
        }

        if (MessageUtils::isResponse(json)) {
            // Use registry to determine the correct response type
            if (registry) {
                QVariant id = MessageUtils::getId(json);
                QString method = registry->getPendingRequestMethod(id);

                auto it = m_responseCreators.find(method);
                if (it != m_responseCreators.end()) {
                    return it.value()(json);
                }
            }
            // Fallback to generic response
            QVariant id = MessageUtils::getId(json);
            QJsonValue result = json["result"];
            return std::make_unique<GenericResponse>(id, result);
        }

        if (MessageUtils::isNotification(json)) {
            QString method = MessageUtils::getMethod(json);
            auto it = m_notificationCreators.find(method);
            if (it != m_notificationCreators.end()) {
                return it.value()(json);
            }
            // Create generic notification
            QJsonObject params = json["params"].toObject();
            return std::make_unique<GenericNotification>(method, params);
        }
    } catch (const std::exception& e) {
        qWarning() << "Exception creating message:" << e.what();
        return nullptr;
    }

    qWarning() << "Unknown message type";
    return nullptr;
}

void LspMessageFactory::registerMessageCreator(const QString& method, MessageCreator creator) {
    // Determine if this is for requests, responses, or notifications based on method name
    if (method.startsWith("$/")) {
        m_notificationCreators[method] = creator;
    } else {
        // For now, assume it's a request. In a more sophisticated implementation,
        // we might need separate registration methods for requests vs notifications
        m_requestCreators[method] = creator;
    }
}

bool LspMessageFactory::isMethodSupported(const QString& method) const {
    return m_requestCreators.contains(method) || 
           m_notificationCreators.contains(method) ||
           m_responseCreators.contains(method);
}

QStringList LspMessageFactory::getSupportedMethods() const {
    QStringList methods;
    methods.append(m_requestCreators.keys());
    methods.append(m_notificationCreators.keys());
    methods.append(m_responseCreators.keys());
    return methods;
}

QByteArray LspMessageFactory::serializeMessage(const LspMessage& message) {
    QJsonObject json = message.toJson();
    QJsonDocument doc(json);
    QByteArray content = doc.toJson(QJsonDocument::Compact);

    // Add LSP headers
    QByteArray header = "Content-Length: " + QByteArray::number(content.size()) + "\r\n\r\n";
    return header + content;
}

std::unique_ptr<LspMessage> LspMessageFactory::parseMessage(const QByteArray& data, int& bytesConsumed, LspMessageRegistry* registry) {
    bytesConsumed = 0;

    // Find the end of headers
    int headerEnd = data.indexOf("\r\n\r\n");
    if (headerEnd == -1) {
        return nullptr; // Incomplete headers
    }

    // Parse Content-Length header
    QByteArray headers = data.left(headerEnd);
    QList<QByteArray> headerLines = headers.split('\n');

    int contentLength = -1;
    for (const QByteArray& line : headerLines) {
        if (line.startsWith("Content-Length:")) {
            QByteArray lengthStr = line.mid(15).trimmed();
            bool ok;
            contentLength = lengthStr.toInt(&ok);
            if (!ok) {
                return nullptr;
            }
            break;
        }
    }

    if (contentLength == -1) {
        return nullptr; // No Content-Length header
    }

    int contentStart = headerEnd + 4;
    if (data.size() < contentStart + contentLength) {
        return nullptr; // Incomplete content
    }

    // Extract JSON content
    QByteArray jsonData = data.mid(contentStart, contentLength);
    qInfo() << "Parsing JSON content:" << jsonData;
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonData, &error);

    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return nullptr;
    }

    bytesConsumed = contentStart + contentLength;
    return createMessage(doc.object(), registry);
}

QList<const QMetaObject*>& LspMessageFactory::getRegisteredMetaObjects() {
    static QList<const QMetaObject*> registeredMetaObjects;
    return registeredMetaObjects;
}

QHash<QString, std::function<std::unique_ptr<LspMessage>(const QJsonObject&)>>& LspMessageFactory::getFactoryRegistry() {
    static QHash<QString, std::function<std::unique_ptr<LspMessage>(const QJsonObject&)>> factoryRegistry;
    return factoryRegistry;
}

void LspMessageFactory::registerMetaObject(const QMetaObject* metaObject) {
    if (metaObject) {
        getRegisteredMetaObjects().append(metaObject);
    }
}

void LspMessageFactory::registerFactory(const QString& className, std::function<std::unique_ptr<LspMessage>(const QJsonObject&)> factory) {
    getFactoryRegistry()[className] = factory;
    qDebug() << "Registered factory for class:" << className;
}

void LspMessageFactory::registerLspMessageTypes() {
    // Register the LspMessage* type with Qt's meta-type system
    qRegisterMetaType<LspMessage*>("LspMessage*");
    qRegisterMetaType<QJsonObject>("QJsonObject");

    qDebug() << "LSP message types will be registered automatically via static initialization";
}

void LspMessageFactory::registerAllMessageClasses() {
    // Register all LSP message classes that have been automatically discovered
    qDebug() << "Registering automatically discovered LSP message classes...";

    int registeredCount = 0;

    // Use our static registry of LSP message classes
    const auto& metaObjects = getRegisteredMetaObjects();

    for (const QMetaObject* metaObject : metaObjects) {
        if (!metaObject) {
            continue;
        }

        // Verify this class has LSP metadata (should always be true for registered classes)
        int methodIndex = metaObject->indexOfClassInfo("LSP_METHOD");
        int typeIndex = metaObject->indexOfClassInfo("LSP_TYPE");

        if (methodIndex >= 0 && typeIndex >= 0) {
            // Register this LSP message class
            registerMessageClass(metaObject);
            registeredCount++;
        } else {
            qWarning() << "Class" << metaObject->className() << "was registered but lacks LSP metadata";
        }
    }

    qDebug() << "Automatically registered" << registeredCount << "LSP message classes";
}

void LspMessageFactory::registerMessageClass(const QMetaObject* metaObject) {
    int methodIndex = metaObject->indexOfClassInfo("LSP_METHOD");
    int typeIndex = metaObject->indexOfClassInfo("LSP_TYPE");

    if (methodIndex < 0 || typeIndex < 0) {
        return;
    }

    QString method = QString::fromLatin1(metaObject->classInfo(methodIndex).value());
    QString type = QString::fromLatin1(metaObject->classInfo(typeIndex).value());

    MessageCreator creator = createMessageCreator(metaObject);

    if (type == "request") {
        m_requestCreators[method] = creator;
        qDebug() << "Auto-registered request:" << method << "from class" << metaObject->className();
    } else if (type == "response") {
        m_responseCreators[method] = creator;
        qDebug() << "Auto-registered response:" << method << "from class" << metaObject->className();
    } else if (type == "notification") {
        m_notificationCreators[method] = creator;
        qDebug() << "Auto-registered notification:" << method << "from class" << metaObject->className();
    }
}

LspMessageFactory::MessageCreator LspMessageFactory::createMessageCreator(const QMetaObject* metaObject) {
    // Use the automatically registered factory function for this class
    // This achieves true automatic registration with no hardcoded lists
    QString className = QString::fromLatin1(metaObject->className());

    // Extract just the class name without namespace
    QString simpleClassName = className;
    if (className.contains("::")) {
        simpleClassName = className.split("::").last();
    }

    return [simpleClassName](const QJsonObject& json) -> std::unique_ptr<LspMessage> {
        const auto& factoryRegistry = getFactoryRegistry();

        if (factoryRegistry.contains(simpleClassName)) {
            auto factory = factoryRegistry[simpleClassName];
            auto result = factory(json);
            if (result) {
                qDebug() << "Successfully created message using auto-registered factory for class:" << simpleClassName;
                return result;
            }
        }

        qWarning() << "No factory found for class:" << simpleClassName;
        return nullptr;
    };
}

// MessageUtils implementation
namespace MessageUtils {

bool isRequest(const QJsonObject& json) {
    return json.contains("id") && json.contains("method") && !json.contains("result") && !json.contains("error");
}

bool isResponse(const QJsonObject& json) {
    return json.contains("id") && (json.contains("result") || json.contains("error")) && !json.contains("method");
}

bool isNotification(const QJsonObject& json) {
    return json.contains("method") && !json.contains("id");
}

bool isError(const QJsonObject& json) {
    return json.contains("id") && json.contains("error");
}

QString getMethod(const QJsonObject& json) {
    return json["method"].toString();
}

QVariant getId(const QJsonObject& json) {
    return json["id"].toVariant();
}

bool isValidJsonRpc(const QJsonObject& json) {
    return json["jsonrpc"].toString() == "2.0";
}

QJsonObject createMethodNotFoundError(const QVariant& id, const QString& method) {
    QJsonObject error;
    error["code"] = -32601; // MethodNotFound
    error["message"] = QString("Method not found: %1").arg(method);

    QJsonObject response;
    response["jsonrpc"] = "2.0";
    response["id"] = QJsonValue::fromVariant(id);
    response["error"] = error;
    return response;
}

QJsonObject createInvalidParamsError(const QVariant& id, const QString& message) {
    QJsonObject error;
    error["code"] = -32602; // InvalidParams
    error["message"] = message.isEmpty() ? "Invalid params" : message;

    QJsonObject response;
    response["jsonrpc"] = "2.0";
    response["id"] = QJsonValue::fromVariant(id);
    response["error"] = error;
    return response;
}

QJsonObject createInternalError(const QVariant& id, const QString& message) {
    QJsonObject error;
    error["code"] = -32603; // InternalError
    error["message"] = message.isEmpty() ? "Internal error" : message;

    QJsonObject response;
    response["jsonrpc"] = "2.0";
    response["id"] = QJsonValue::fromVariant(id);
    response["error"] = error;
    return response;
}

} // namespace MessageUtils

} // namespace Lsp
